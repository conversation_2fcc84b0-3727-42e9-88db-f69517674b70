local WebSocket = require('websocket')
local EventEmitter = require('events').EventEmitter

local WSClient = {}
WSClient.__index = WSClient

function WSClient:new(url)
    local self = setmetatable({}, WSClient)
    self.url = url
    self.client = nil
    self.events = EventEmitter:new()
    return self
end

function WSClient:connect()
    self.client = WebSocket.connect(self.url, function(ws)
        -- 连接成功
        self.events:emit('open')

        ws:on('message', function(data)
            self.events:emit('message', data)
        end)

        ws:on('close', function()
            self.events:emit('close')
        end)

        ws:on('error', function(err)
            self.events:emit('error', err)
        end)
    end)
end

function WSClient:send(data)
    if self.client then
        self.client:send(data)
    end
end

function WSClient:on(event, callback)
    self.events:on(event, callback)
end

function WSClient:close()
    if self.client then
        self.client:close()
    end
end

return WSClient
